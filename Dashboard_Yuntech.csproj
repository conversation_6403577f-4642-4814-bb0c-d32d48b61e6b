﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Blazor-ApexCharts" Version="5.1.0" />
    <PackageReference Include="Blazorise.Bootstrap5" Version="1.7.5" />
    <PackageReference Include="Blazorise.Charts" Version="1.7.5" />
    <PackageReference Include="Blazorise.Charts.DataLabels" Version="1.7.5" />
    <PackageReference Include="Blazorise.DataGrid" Version="1.7.5" />
    <PackageReference Include="Blazorise.Icons.FontAwesome" Version="1.7.5" />
    <PackageReference Include="EPPlus" Version="7.5.2" />
    <PackageReference Include="ExcelDataReader" Version="3.7.0" />
    <PackageReference Include="ExcelDataReader.DataSet" Version="3.7.0" />
    <PackageReference Include="HtmlAgilityPack" Version="1.11.71" />
    <PackageReference Include="Microsoft.AspNetCore.Components.QuickGrid.EntityFrameworkAdapter" Version="8.0.2" />
    <PackageReference Include="Microsoft.AspNetCore.Session" Version="2.3.0" />
    <PackageReference Include="Microsoft.Azure.Cosmos" Version="3.51.0" />
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="9.0.0" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="9.0.0" />
    <PackageReference Include="Microsoft.Extensions.Caching.Memory" Version="9.0.1" />
    <PackageReference Include="Microsoft.Extensions.Http" Version="9.0.1" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="Oracle.EntityFrameworkCore" Version="9.23.60" />
    <PackageReference Include="System.Text.Encoding.CodePages" Version="8.0.0" />
    <PackageReference Include="YuntechNET.Core" Version="1.0.4" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Components\Pages\Admin\" />
  </ItemGroup>

  <ItemGroup>
    <Content Update="Components\Pages\Common\SelectInput.razor">
      <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
    </Content>
  </ItemGroup>

</Project>
