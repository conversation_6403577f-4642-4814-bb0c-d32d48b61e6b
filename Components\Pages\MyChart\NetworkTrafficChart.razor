@using ApexCharts
@using Dashboard_Yuntech.Models.ChartModels
@using Dashboard_Yuntech.Service

<div class="card">
    <div class="card-header">
        <h5 class="card-title">@Title</h5>
    </div>
    <div class="card-body">
        @if (IsLoading)
        {
            <div class="text-center mt-5">
                <div class="spinner-border" role="status">
                    <span class="visually-hidden">載入中...</span>
                </div>
            </div>
        }
        else if (!string.IsNullOrEmpty(ErrorMessage))
        {
            <div class="alert alert-danger">@ErrorMessage</div>
        }
        else if (ChartData == null || !ChartData.Any())
        {
            <div class="alert alert-info">沒有可顯示的數據</div>
        }
        else
        {
            <ApexChart TItem="NetworkTrafficModel" Options="ChartOptions">
                <ApexPointSeries TItem="NetworkTrafficModel"
                                Name="Inbound (Mbps)"
                                Items="ChartData"
                                XValue="@(e => e.DateString)"
                                YValue="@(e => (decimal)e.InboundMbps)"
                                SeriesType="SeriesType.Line" />
                
                <ApexPointSeries TItem="NetworkTrafficModel"
                                Name="Outbound (Mbps)"
                                Items="ChartData"
                                XValue="@(e => e.DateString)"
                                YValue="@(e => (decimal)e.OutboundMbps)"
                                SeriesType="SeriesType.Line" />
            </ApexChart>
        }
    </div>
</div>

@code {
    [Parameter] public string Title { get; set; } = "網路流量統計";
    [Parameter] public List<NetworkTrafficModel> ChartData { get; set; } = new();
    [Parameter] public bool IsLoading { get; set; } = false;
    [Parameter] public string ErrorMessage { get; set; } = "";

    private ApexChartOptions<NetworkTrafficModel> ChartOptions = new();

    protected override void OnInitialized()
    {
        ConfigureChart();
    }

    protected override void OnParametersSet()
    {
        ConfigureChart();
    }

    private void ConfigureChart()
    {
        ChartOptions = new ApexChartOptions<NetworkTrafficModel>
        {
            Theme = new Theme
            {
                Mode = Mode.Light,
                Palette = PaletteType.Palette1
            },
            Chart = new Chart
            {
                Type = ChartType.Line,
                Height = 400,
                Zoom = new Zoom
                {
                    Enabled = true
                },
                Toolbar = new Toolbar
                {
                    Show = true
                }
            },
            Stroke = new Stroke
            {
                Curve = Curve.Smooth,
                Width = 2
            },
            Colors = new List<string> { "#008FFB", "#00E396" },
            Xaxis = new XAxis
            {
                Type = XAxisType.Category,
                Title = new AxisTitle
                {
                    Text = "時間"
                },
                Labels = new XAxisLabels
                {
                    Rotate = -45,
                    MaxHeight = 120
                }
            },
            Yaxis = new List<YAxis>
            {
                new YAxis
                {
                    Title = new AxisTitle
                    {
                        Text = "流量 (Mbps)"
                    },
                    Labels = new YAxisLabels
                    {
                        Formatter = "function(value) { return value.toFixed(2) + ' Mbps'; }"
                    }
                }
            },
            Tooltip = new Tooltip
            {
                Shared = true,
                Intersect = false,
                Y = new TooltipY
                {
                    Formatter = "function(value) { return value.toFixed(2) + ' Mbps'; }"
                }
            },
            Legend = new Legend
            {
                Position = LegendPosition.Top,
                HorizontalAlign = Align.Center
            },
            Grid = new Grid
            {
                BorderColor = "#e0e6ed",
                StrokeDashArray = 5,
                Xaxis = new GridXAxis
                {
                    Lines = new GridLines
                    {
                        Show = true
                    }
                },
                Yaxis = new GridYAxis
                {
                    Lines = new GridLines
                    {
                        Show = true
                    }
                }
            }
        };
    }
}
