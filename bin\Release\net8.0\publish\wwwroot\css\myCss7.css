/*SweetAlert2 Toast*/
.colored-toast.swal2-icon-success {
    background-color: #a5dc86 !important;
}

.colored-toast.swal2-icon-error {
    background-color: #f27474 !important;
}

.colored-toast.swal2-icon-warning {
    background-color: #f8bb86 !important;
}

.colored-toast.swal2-icon-info {
    background-color: #3fc3ee !important;
}

.colored-toast.swal2-icon-question {
    background-color: #87adbd !important;
}

.colored-toast .swal2-title {
    color: white;
}

.colored-toast .swal2-close {
    color: white;
}

.colored-toast .swal2-html-container {
    color: white;
}

.colored-toast-title {
    color: white !important;
}

.colored-toast-close-button {
    color: white !important;
}


.borderTitle {
    border-color: #8bc9db !important;
}

.pageTitle {
    letter-spacing: 1.5px;
}

.hidden {
    display: none !important;
}

.card {
    background-color: white;
    border-radius: 8px !important;
/*    padding: 0.8rem 0.5rem !important;*/
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    border: 0px !important;
}

.myLabel {
    color: #1a3a5f !important;
    font-weight:bold !important;
}

.card-header {
    background-color: transparent !important;
    padding-right: 0px !important;
    padding-left: 0px !important;
    margin-right: 1rem !important;
    margin-left: 1rem !important;
    border-bottom: #3498db solid 2px;
}

th {
    background-color: #f5f5f5 !important;
}

.text-darkBlue {
    color: #1a3a5f;
}

.fs-6 {
    font-size:14px;
}

.table-responsive {
    max-height: 70vh;
    overflow-y: auto;
    overflow-x: auto;
}

/* ApexCharts Tooltip 自定義樣式 */
.apexcharts-tooltip-text {
    margin-left: 10px !important;
}