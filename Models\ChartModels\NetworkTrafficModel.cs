using System;
using System.Collections.Generic;

namespace Dashboard_Yuntech.Models.ChartModels
{
    public class NetworkTrafficModel
    {
        public DateTime Date { get; set; }
        public long Inbound { get; set; }
        public long Outbound { get; set; }

        // 用於圖表顯示的格式化屬性
        public string DateString => Date.ToString("yyyy/MM/dd HH:mm");
        public decimal InboundMbps => Math.Round((decimal)(Inbound / 1_000_000.0), 2); // 轉換為 Mbps
        public decimal OutboundMbps => Math.Round((decimal)(Outbound / 1_000_000.0), 2); // 轉換為 Mbps

        // 用於 ExcelChart2 多系列支援
        public Dictionary<string, decimal> ValueColumns => new Dictionary<string, decimal>
        {
            ["Inbound (Mbps)"] = InboundMbps,
            ["Outbound (Mbps)"] = OutboundMbps
        };
    }
}
