using System;

namespace Dashboard_Yuntech.Models.ChartModels
{
    public class NetworkTrafficModel
    {
        public DateTime Date { get; set; }
        public long Inbound { get; set; }
        public long Outbound { get; set; }
        
        // 用於圖表顯示的格式化屬性
        public string DateString => Date.ToString("yyyy/MM/dd HH:mm");
        public double InboundMbps => Math.Round(Inbound / 1_000_000.0, 2); // 轉換為 Mbps
        public double OutboundMbps => Math.Round(Outbound / 1_000_000.0, 2); // 轉換為 Mbps
    }
}
