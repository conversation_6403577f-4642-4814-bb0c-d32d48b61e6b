@page "/Internet"
@using System.Diagnostics
@using Dashboard_Yuntech.Service
@using Dashboard_Yuntech.Models.ChartModels
@using Dashboard_Yuntech.Components.Pages.MyChart
@rendermode InteractiveServer
@inject WebScrapingService WebScrapingService
@inject IJSRuntime JSRuntime

<PageTitle>網路流量爬蟲</PageTitle>

<MyAlert Text="網路流量"></MyAlert>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">YUNTECH 網路流量監控</h5>
                </div>
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-md-8">
                            <label for="urlInput" class="form-label">Cacti 圖表 URL:</label>
                            <input type="text" class="form-control" id="urlInput" @bind="cactiUrl"
                                   placeholder="http://cnms.yuntech.edu.tw/cacti/graph.php?action=view&local_graph_id=1711&rra_id=all" />
                        </div>
                        <div class="col-md-4">
                            <label for="timeRangeSelect" class="form-label">時間範圍:</label>
                            <select class="form-select" id="timeRangeSelect" @bind="selectedTimeRange">
                                <option value="Hourly">Hourly (1 Minute Average)</option>
                                <option value="Daily">Daily (5 Minute Average)</option>
                                <option value="Weekly">Weekly (30 Minute Average)</option>
                                <option value="Monthly">Monthly (2 Hour Average)</option>
                                <option value="Yearly">Yearly (1 Day Average)</option>
                            </select>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-12">
                            <button class="btn btn-primary me-2" @onclick="ScrapeAndDownloadCsv" disabled="@isLoading">
                                @if (isLoading)
                                {
                                    <span class="spinner-border spinner-border-sm me-2" role="status"></span>
                                }
                                爬取並下載 CSV
                            </button>
                            <button class="btn btn-success me-2" @onclick="DownloadCsvFile" disabled="@(string.IsNullOrEmpty(csvContent))">
                                下載 CSV 檔案
                            </button>
                            <button class="btn btn-secondary" @onclick="ClearData">
                                清除資料
                            </button>
                        </div>
                    </div>

                    @if (!string.IsNullOrEmpty(errorMessage))
                    {
                        <div class="alert alert-danger" role="alert">
                            <strong>錯誤:</strong> @errorMessage
                        </div>
                    }

                    @if (!string.IsNullOrEmpty(successMessage))
                    {
                        <div class="alert alert-success" role="alert">
                            <strong>成功:</strong> @successMessage
                        </div>
                    }

                    @if (csvData != null && csvData.Any())
                    {
                        <div class="mt-4">
                            <h6>CSV 資料預覽 (前 10 筆):</h6>
                            <div class="table-responsive">
                                <table class="table table-striped table-hover">
                                    <thead class="table-dark">
                                        <tr>
                                            @if (csvData.First().Keys != null)
                                            {
                                                @foreach (var header in csvData.First().Keys)
                                                {
                                                    <th>@header</th>
                                                }
                                            }
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach (var row in csvData.Take(10))
                                        {
                                            <tr>
                                                @foreach (var value in row.Values)
                                                {
                                                    <td>@value</td>
                                                }
                                            </tr>
                                        }
                                    </tbody>
                                </table>
                            </div>

                            @if (csvData.Count > 10)
                            {
                                <p class="text-muted">顯示前 10 筆，共 @csvData.Count 筆資料</p>
                            }
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>

    @* 圖表區域 *@
    @if (trafficChartData != null && trafficChartData.Any())
    {
        <div class="row mt-4">
            <div class="col-12">
                <NetworkTrafficChart Title="@($"網路流量統計圖 - {selectedTimeRange}")"
                                   ChartData="trafficChartData"
                                   IsLoading="isLoading"
                                   ErrorMessage="@errorMessage" />
            </div>
        </div>
    }
</div>

@code {
    private string cactiUrl = "http://cnms.yuntech.edu.tw/cacti/graph.php?action=view&local_graph_id=1711&rra_id=all";
    private string selectedTimeRange = "Hourly";
    private string csvContent = "";
    private List<Dictionary<string, object>> csvData = new();
    private List<NetworkTrafficModel> trafficChartData = new();
    private bool isLoading = false;
    private string errorMessage = "";
    private string successMessage = "";

    private async Task ScrapeAndDownloadCsv()
    {
        try
        {
            isLoading = true;
            errorMessage = "";
            successMessage = "";
            StateHasChanged();

            if (string.IsNullOrWhiteSpace(cactiUrl))
            {
                errorMessage = "請輸入 Cacti 圖表 URL";
                return;
            }

            // 爬取網頁並下載 CSV
            csvContent = await WebScrapingService.ScrapeAndDownloadCsvAsync(cactiUrl, selectedTimeRange);

            if (string.IsNullOrWhiteSpace(csvContent))
            {
                errorMessage = "下載的 CSV 內容為空";
                return;
            }

            // 解析 CSV 資料
            csvData = WebScrapingService.ParseCsvContent(csvContent);

            // 解析 CSV 資料為圖表資料
            trafficChartData = WebScrapingService.ParseCsvToTrafficData(csvContent);

            if (csvData.Any())
            {
                successMessage = $"成功下載並解析 CSV 資料，共 {csvData.Count} 筆記錄，圖表資料 {trafficChartData.Count} 筆";
            }
            else
            {
                errorMessage = "CSV 資料解析失敗或無資料";
            }
        }
        catch (Exception ex)
        {
            errorMessage = $"操作失敗: {ex.Message}";
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }

    private async Task DownloadCsvFile()
    {
        try
        {
            if (string.IsNullOrEmpty(csvContent))
            {
                errorMessage = "沒有可下載的 CSV 資料";
                return;
            }

            var fileName = $"yuntech_traffic_{selectedTimeRange}_{DateTime.Now:yyyyMMdd_HHmmss}.csv";
            var bytes = System.Text.Encoding.UTF8.GetBytes(csvContent);
            var base64 = Convert.ToBase64String(bytes);

            await JSRuntime.InvokeVoidAsync("downloadFile", fileName, base64, "text/csv");

            successMessage = $"CSV 檔案 {fileName} 已下載";
            errorMessage = "";
        }
        catch (Exception ex)
        {
            errorMessage = $"下載檔案失敗: {ex.Message}";
        }
    }

    private void ClearData()
    {
        csvContent = "";
        csvData.Clear();
        trafficChartData.Clear();
        errorMessage = "";
        successMessage = "";
        StateHasChanged();
    }
}