{"format": 1, "restore": {"C:\\Users\\<USER>\\Desktop\\儀錶板2\\Dashboard_Yuntech\\Dashboard_Yuntech\\Dashboard_Yuntech.csproj": {}}, "projects": {"C:\\Users\\<USER>\\Desktop\\儀錶板2\\Dashboard_Yuntech\\Dashboard_Yuntech\\Dashboard_Yuntech.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Desktop\\儀錶板2\\Dashboard_Yuntech\\Dashboard_Yuntech\\Dashboard_Yuntech.csproj", "projectName": "Dashboard_Yuntech", "projectPath": "C:\\Users\\<USER>\\Desktop\\儀錶板2\\Dashboard_Yuntech\\Dashboard_Yuntech\\Dashboard_Yuntech.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Desktop\\儀錶板2\\Dashboard_Yuntech\\Dashboard_Yuntech\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}, "https://pkgs.dev.azure.com/YuntechTCX/\\_packaging/YuntechTCX/nuget/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Blazor-ApexCharts": {"target": "Package", "version": "[5.1.0, )"}, "Blazorise.Bootstrap5": {"target": "Package", "version": "[1.7.5, )"}, "Blazorise.Charts": {"target": "Package", "version": "[1.7.5, )"}, "Blazorise.Charts.DataLabels": {"target": "Package", "version": "[1.7.5, )"}, "Blazorise.DataGrid": {"target": "Package", "version": "[1.7.5, )"}, "Blazorise.Icons.FontAwesome": {"target": "Package", "version": "[1.7.5, )"}, "EPPlus": {"target": "Package", "version": "[7.5.2, )"}, "ExcelDataReader": {"target": "Package", "version": "[3.7.0, )"}, "ExcelDataReader.DataSet": {"target": "Package", "version": "[3.7.0, )"}, "HtmlAgilityPack": {"target": "Package", "version": "[1.11.71, )"}, "Microsoft.AspNetCore.Components.QuickGrid.EntityFrameworkAdapter": {"target": "Package", "version": "[8.0.2, )"}, "Microsoft.AspNetCore.Session": {"target": "Package", "version": "[2.3.0, )"}, "Microsoft.Azure.Cosmos": {"target": "Package", "version": "[3.51.0, )"}, "Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.EntityFrameworkCore.SqlServer": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.Extensions.Caching.Memory": {"target": "Package", "version": "[9.0.1, )"}, "Microsoft.Extensions.Http": {"target": "Package", "version": "[9.0.1, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}, "Oracle.EntityFrameworkCore": {"target": "Package", "version": "[9.23.60, )"}, "System.Text.Encoding.CodePages": {"target": "Package", "version": "[8.0.0, )"}, "YuntechNET.Core": {"target": "Package", "version": "[1.0.4, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.302/PortableRuntimeIdentifierGraph.json"}}}}}