:root {
	--vh: 1vh;
	--color-background: #121212;
	--color-card-background: #1E1E1E;
	--color-border: #2A2A2A;
	--color-highlight: #5a9cf8;
	--color-normal-text: white;
	--color-complement-text: #888787;
	--color-component-background: #1A1A1A;
	--color-overlay: rgba(0, 0, 0, 0.65);
	--color-nav-background: #171717;
	--color-sidebar-background: #212121;
	--font-to-icon: 1.2;
	--font-xl: 1.5rem;
	--font-l: 1.25rem;
	--font-m: 18px;
	--font-ms: 1rem;
	--font-s: 0.75rem;
	--font-icon: "Font Awesome 6 Free";
	--nav-height: 55px;
	--sidebar-width: 190px;
	--sidebar-item-collapsed-width: 80px;
	--color-taipei: #1411AC;
	--color-metrotaipei: #ac7811;
	--color-component-background:#282a2c;
	--color-background:#090909;
	--color-border:#494b4e;
}

/* 明亮模式變數 */
.light-mode {
	--color-background: #f5f5f5;
	--color-card-background: #ffffff;
	--color-border: #e0e0e0;
	--color-highlight: #2979ff;
	--color-normal-text: #333333;
	--color-complement-text: #767676;
	--color-component-background: #ffffff;
	--color-overlay: rgba(255, 255, 255, 0.75);
	--color-nav-background: #ffffff;
	--color-sidebar-background: #f9f9f9;
	--color-taipei: #0d47a1;
	--color-metrotaipei: #ef6c00;
	--color-background: #f0f2f5;
	--color-border: #d1d5db;
}

/* 禁用側邊欄過渡動畫的類 */
.no-sidebar-transition {
	transition: none !important;
}

/* 明亮模式下的卡片和元素樣式修正 */
.light-mode .card {
	background-color: #ffffff;
	color: #333333;
}

.light-mode .logo-icon {
	background-color: #0d47a1;
}

.light-mode .logo-main {
	color: #333333;
}

.light-mode .sidebar-title {
	color: #333333;
}

.light-mode .side-bar-menu-item {
	color: #333333;
}

.light-mode .menu-item-short-text {
	color: #767676;
}

.light-mode .side-bar-menu-item.active .menu-item-icon,
.light-mode .side-bar-menu-item.active .menu-item-text,
.light-mode .side-bar-menu-item.active .menu-item-short-text {
	color: var(--color-highlight);
}

.light-mode .content-title {
	color: #333333;
}

.light-mode .text-white {
	color: #333333 !important;
}

/* 樣式切換按鈕 */
.theme-toggle-btn {
	cursor: pointer;
	padding: 5px 8px;
	border-radius: 3px;
	transition: all 0.3s ease;
}

.theme-toggle-btn:hover {
	background-color: rgba(255, 255, 255, 0.1);
	color: var(--color-highlight);
}

.light-mode .theme-toggle-btn:hover {
	background-color: rgba(0, 0, 0, 0.05);
}

/* 切換按鈕動畫 */
.theme-toggle-btn i {
	transition: transform 0.5s ease;
}

.theme-toggle-btn:hover i {
	transform: rotate(30deg);
}

* {
	margin: 0;
	padding: 0;
	font-family: "微軟正黑體", "Microsoft JhengHei", "Droid Sans", "Open Sans", "Helvetica";
	box-sizing: border-box;
}

h1 {
	font-size: var(--font-l);
}

h2 {
	font-size: var(--font-m);
}

h3, p {
	font-size: var(--font-s);
}

a {
	color: var(--color-normal-text);
	text-decoration: none;
	&:hover {
		color: var(--color-highlight);
	}
}

.light-mode a {
	color: var(--color-normal-text);
	&:hover {
		color: var(--color-highlight);
	}
}

button {
	border: none;
	background-color: transparent;
	cursor: pointer;
}

body {
	background-color: var(--color-background);
	overflow: auto;
	color: var(--color-normal-text);
}

.app-container {
	max-width: 100vw;
	max-height: 100vh;
	max-height: calc(var(--vh) * 100);
}

.nav-bar {
	height: 65px;
	background-color: var(--color-component-background);
	display: flex;
	align-items: center;
	padding: 0 10px;
	justify-content: space-between;
	border-bottom: 1px solid var(--color-border);
	position: sticky;
	top: 0;
	z-index: 100;
	width: 100%;
}

.logo-container {
	display: flex;
	align-items: center;
	gap: 12px;
	padding-left: 10px;
}

.logo-icon {
	width: 24px;
	height: 24px;
	background-color: white;
	border-radius: 3px;
}

.logo {
	display: flex;
	flex-direction: column;
}

.logo-main {
	font-size: var(--font-m);
	font-weight: bold;
	line-height: 1;
}

.logo-sub {
	font-size: var(--font-s);
	color: var(--color-normal-text);
	line-height: 1.3;
}

.nav-links {
	display: flex;
	gap: 20px;
}

.nav-links a {
	color: var(--color-complement-text);
	text-decoration: none;
	font-size: var(--font-s);
	transition: color 0.3s;
}

.nav-links a:hover {
	color: var(--color-highlight);
}

.nav-actions {
	display: flex;
	align-items: center;
	gap: 15px;
}

.nav-action-btn {
	color: var(--color-complement-text);
	font-size: var(--font-m);
	cursor: pointer;
}

.app-content {
	width: 100vw;
	max-width: 100vw;
	height: calc(100vh - 65px);
	height: calc(var(--vh) * 100 - 65px);
	display: flex;
	position: relative;
	overflow: hidden;
	scroll-behavior: smooth;
}

.sidebar-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 6px 10px;
	padding-bottom: 10px;
	transition: all 0.3s ease;
}

.sidebar-title {
	font-size: var(--font-m);
	font-weight: bold;
	transition: opacity 0.2s ease, width 0.2s ease;
	white-space: nowrap;
}

.side-bar.collapsed_sideBar .sidebar-header {
	padding: 14px 0;
	justify-content: center;
}

.side-bar.collapsed_sideBar .sidebar-title {
	opacity: 0;
	width: 0;
	margin: 0;
	padding: 0;
	display: none;
}

.sidebar-toggle-btn {
	font-family: var(--font-icon);
	font-size: var(--font-l);
	color: var(--color-complement-text);
	transition: all 0.3s;
	cursor: pointer;
	display: flex;
	align-items: center;
	justify-content: center;
}

.sidebar-toggle-btn:hover {
	color: var(--color-highlight);
}

.side-bar {
	width: var(--sidebar-width);
	min-width: var(--sidebar-width);
	overflow-y: auto;
	transition: all 0.3s ease;
	border-right: 1px solid var(--color-border);
	display: flex;
	flex-direction: column;
	margin-top: 20px;
	margin-left: 8px;
	margin-right: 8px;
}

.side-bar.collapsed_sideBar {
	width: var(--sidebar-item-collapsed-width);
	min-width: var(--sidebar-item-collapsed-width);
}

.side-bar-menu {
	display: flex;
	flex-direction: column;
	margin:0px 8px;
}

.sidebar-item-collapse{
	font-size: 16px;
	padding: 6px 26px;
	border-radius: 8px;
	transition: all 0.3s ease;
	display: flex;
	align-items: center;
	&:hover{
		background-color: var(--color-component-background);
	}
}

.sidebar-item-collapse.collapsed{
	color: var(--color-highlight);
	background-color: var(--color-component-background);
}

.sidebar-item-collapse-lg .collapse-long-text {
	display: block;
}

.sidebar-item-collapse-lg .collapse-short-text {
	display: none;
}

.side-bar.collapsed_sideBar .sidebar-item-collapse {
	justify-content: center;
	padding: 6px 1px;
	text-align: center;
}

.side-bar.collapsed_sideBar .sidebar-item-collapse-lg .collapse-long-text {
	display: none;
}

.side-bar.collapsed_sideBar .sidebar-item-collapse-lg .collapse-short-text {
	display: inline-block;
	font-size: var(--font-s);
	margin-right: 5px;
}

.side-bar.collapsed_sideBar .sidebar-item-collapse .text-sm {
	display: inline-flex;
	align-items: center;
}

.sidebar-item-collapse .fa-angle-down {
	transform: rotate(0);
	transition: transform 0.3s;
	font-size: 10px;
}

.sidebar-item-collapse[aria-expanded="true"] .fa-angle-down {
	transform: rotate(180deg);
}

.side-bar-menu-item {
	padding: 4px 14px;
	cursor: pointer;
	transition: all 0.3s;
	display: flex;
	align-items: center;
	white-space: nowrap;
	overflow: hidden;
	border-left: 10px solid transparent;
	position: relative;
	border-radius: 8px;
	margin-bottom: 5px;
	font-size: 18px;
}

.side-bar.collapsed_sideBar .side-bar-menu-item {
	justify-content: center;
	padding-bottom: 14px;
}

.menu-item-icon {
	font-size: var(--font-m);
	margin-right: 10px;
	min-width: 24px;
	display: flex;
	align-items: center;
	justify-content: center;
	transition: all 0.3s ease;
}

.side-bar.collapsed_sideBar .menu-item-icon {
	font-size: var(--font-ms);
	margin-right: 0px;
	margin-bottom: 10px;
	min-width: 0px;
	display: block;
	align-items: center;
	justify-content: center;
	transition: all 0.3s ease;
}

.menu-item-text {
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
	transition: opacity 0.3s;
}

.side-bar.collapsed_sideBar .menu-item-text {
	opacity: 0;
	width: 0;
	display: none;
}

.menu-item-short-text {
	display: none;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
	transition: all 0.3s;
	font-size: var(--font-s);
	position: absolute;
	top: 30px;
	left: 50%;
	transform: translateX(-50%);
	text-align: center;
	color: var(--color-complement-text);
}

.side-bar.collapsed_sideBar .menu-item-short-text {
	display: block;
	opacity: 1;
	width: 100%;
	font-size: var(--font-s);
}

.side-bar .side-bar-menu-item.active .menu-item-short-text {
	color: var(--color-highlight);
}

.side-bar .side-bar-menu-item:hover {
	background-color: var(--color-component-background);
}

.side-bar .side-bar-menu-item.active {
	background-color: var(--color-component-background);
	border-left: 10px solid var(--color-highlight);
}

.side-bar .side-bar-menu-item.active .menu-item-short-text {
	color: var(--color-highlight);
}

.dropdown-menu .side-bar-menu-item:hover {
	background-color: #3C3C3C;
}

.light-mode .dropdown-menu .side-bar-menu-item:hover {
	background-color: #e9ecef;
}

.dropdown-menu .side-bar-menu-item.active {
	background-color: #3C3C3C;
	border-left: 10px solid var(--color-highlight);
}

.light-mode .dropdown-menu .side-bar-menu-item.active {
	background-color: #e9ecef;
	border-left: 10px solid var(--color-highlight);
}

.side-bar.collapsed_sideBar .side-bar-menu-item.active {
	border-left: 8px solid var(--color-highlight);
}

.side-bar-menu-item.active .menu-item-icon,
.side-bar-menu-item.active .menu-item-text {
	color: var(--color-highlight);
}

.app-content-main {
	width: 100%;
	display: flex;
	flex-direction: column;
	background-color: var(--color-background);
	transition: margin-left 0.3s ease;
	padding: 15px;
	padding-top: 0;
	overflow-y: auto;
	scroll-behavior: smooth;
}

.content-header {
	position: sticky;
	top: 0;
	background-color: var(--color-background);
	z-index: 50;
	justify-content: space-between;
	align-items: center;
	transition: all 0.3s ease;
	width: 100%;
	padding-bottom: 15px;
}

/* 移除陰影效果 */
.content-header::after {
	display: none; /* 隱藏整個偽元素 */
}

.light-mode .content-header::after {
	display: none; /* 同樣在淺色模式下也隱藏 */
}

.content-title {
	font-size: var(--font-l);
	font-weight: bold;
	border-bottom: 1px solid var(--color-border);
	margin-top: 16px;
	padding-bottom: 4px;
}

/* 滾動條樣式 */
::-webkit-scrollbar {
	width: 5px;
	height: 5px;
}

::-webkit-scrollbar-track {
	background: var(--color-background);
}

::-webkit-scrollbar-thumb {
	background: var(--color-border);
	border-radius: 5px;
}

::-webkit-scrollbar-thumb:hover {
	background: var(--color-highlight);
}

/* 特定元素的滾動條樣式將繼承全域設定 */

.fullscreen-btn {
	cursor: pointer;
	padding: 5px 8px;
	border-radius: 3px;
	transition: all 0.3s ease;
}

.fullscreen-btn:hover {
	background-color: rgba(255, 255, 255, 0.1);
	color: var(--color-highlight);
}

.light-mode .fullscreen-btn:hover {
	background-color: rgba(0, 0, 0, 0.05);
}

/* 下拉選單樣式 */
.dropdown-container {
	position: relative;
	display: inline-block;
}

.dropdown-trigger {
	cursor: pointer;
	padding: 5px 8px;
	border-radius: 3px;
	transition: all 0.3s ease;
	display: flex;
	align-items: center;
	gap: 5px;
}

.dropdown-menu {
	position: absolute;
	top: 100%;
	left: 0;
	width: 240px;
	max-height: 0;
	overflow-y: auto;
	background-color: var(--color-component-background);
	border-radius: 4px;
	border: 1px solid var(--color-border);
	box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
	z-index: 1000;
	transition: all 0.3s ease;
	opacity: 0;
	visibility: hidden;
	margin-top: 10px;
	transform: translateY(-10px);
	pointer-events: none;
	display: none;
	scrollbar-width: thin;
	scrollbar-color: var(--color-border) var(--color-component-background);
}

.dropdown-menu.active {
	opacity: 1;
	visibility: visible;
	min-height: 300px !important;
	transform: translateY(0);
	pointer-events: auto;
	display: block;
	overflow-y: auto;
}

.dropdown-item {
	padding: 10px 15px;
	display: flex;
	align-items: center;
	gap: 10px;
	color: var(--color-normal-text);
	border-bottom: 1px solid var(--color-border);
	transition: all 0.2s ease;
}

.light-mode .dropdown-menu {
	background-color: #ffffff;
	border: 1px solid #d1d5db;
	box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.light-mode .dropdown-item {
	color: #333333;
	border-bottom: 1px solid #e0e0e0;
}

.light-mode .dropdown-item:hover {
	background-color: rgba(41, 121, 255, 0.1);
	color: var(--color-highlight);
}

.dropdown-item:last-child {
	border-bottom: none;
}

.dropdown-item:hover {
	background-color: rgba(90, 156, 248, 0.1);
	color: var(--color-highlight);
}

.dropdown-item i {
	width: 18px;
	text-align: center;
}

.overlay {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background-color: rgba(0, 0, 0, 0.6);
	z-index: 100;
	opacity: 0;
	visibility: hidden;
	transition: all 0.3s ease;
	backdrop-filter: blur(2px);
}

.overlay.active {
	opacity: 1;
	visibility: visible;
}

.text-sm{
	font-size: 12px;
}

@keyframes pulse {
	from {
		text-shadow: 0 0 0px rgba(90, 156, 248, 0);
		transform: scale(0.98);
	}
	to {
		text-shadow: 0 0 15px rgba(90, 156, 248, 0.8);
		transform: scale(1.02);
	}
}

.light-mode .nav-bar {
	background-color: #ffffff;
	border-bottom: 1px solid #e0e0e0;
}

.light-mode .logo-sub {
	color: #767676;
}

/* 修正明亮模式下的側邊欄切換按鈕 */
.light-mode .sidebar-toggle-btn {
	color: #767676;
}

.light-mode .sidebar-toggle-btn:hover {
	color: var(--color-highlight);
}

.light-mode .side-bar {
	border-right: 1px solid #e0e0e0;
	background-color: var(--color-background);
}

.light-mode .side-bar-menu-item:hover {
	background-color: #e9ecef;
}

.light-mode .side-bar-menu-item.active {
	background-color: white;
	border-left: 10px solid var(--color-highlight);
}

.light-mode .sidebar-item-collapse:hover {
	background-color: #e9ecef;
}

.light-mode .sidebar-item-collapse[aria-expanded="true"] {
	color: var(--color-highlight);
	background-color: #e9ecef;
}

/* 網頁內容樣式 */
.web-content {
	flex: 1;
	display: flex;
	flex-direction: column;
	background-color: var(--color-background);
	transition: all 0.3s ease;
	padding: 15px;
	padding-top: 0;
	overflow-y: auto;
	scroll-behavior: smooth;
}

.web-content .card:first-child {
	margin-top: 10px !important;
}

.overlay {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background-color: rgba(0, 0, 0, 0.6);
	z-index: 100;
	opacity: 0;
	visibility: hidden;
	transition: all 0.3s ease;
	backdrop-filter: blur(2px);
}

.overlay.active {
	opacity: 1;
	visibility: visible;
}

/* 響應式佈局 */
@media (max-width: 1000px) {
	.app-content {
		flex-direction: column;
		overflow-y: auto;
	}
	
	.web-content {
		width: 100%;
		overflow: visible;
		padding-top: 5px;
	}
	
	.web-content .card:first-child {
		margin-top: 15px !important;
	}
	
	.content-header {
		min-height: 40px;
	}

	.content-title {
		margin-top: 8px;
	}
} 