﻿// 設置視窗高度變數
function setVH() {
    let vh = window.innerHeight * 0.01;
    document.documentElement.style.setProperty("--vh", `${vh}px`);
}
setVH();
window.addEventListener('resize', setVH);

// 初始化變數
let dropdownTrigger;
let dropdownMenu;
let overlay;

// 側邊欄收合功能
function toggleSidebar() {
    const sidebar = document.getElementById('sidebar');
    const mainContent = document.getElementById('mainContent');
    const webContent = document.getElementById('webContent');
    sidebar.classList.toggle('collapsed_sideBar');
    
    if (sidebar.classList.contains('collapsed_sideBar')) {
        document.querySelector('.sidebar-toggle-btn i').className = 'fa-solid fa-chevron-right';
        localStorage.setItem('sidebarCollapsed', 'true');
    } else {
        document.querySelector('.sidebar-toggle-btn i').className = 'fa-solid fa-chevron-left';
        localStorage.setItem('sidebarCollapsed', 'false');
    }
}

// 應用側邊欄折疊狀態
function applySidebarState() {
    const sidebar = document.getElementById('sidebar');
    if (!sidebar) return;
    
    const sidebarState = localStorage.getItem('sidebarCollapsed');
    if (sidebarState === 'true') {
        sidebar.classList.add('collapsed_sideBar');
        const toggleBtn = document.querySelector('.sidebar-toggle-btn i');
        if (toggleBtn) toggleBtn.className = 'fa-solid fa-chevron-right';
    } else {
        sidebar.classList.remove('collapsed_sideBar');
        const toggleBtn = document.querySelector('.sidebar-toggle-btn i');
        if (toggleBtn) toggleBtn.className = 'fa-solid fa-chevron-left';
    }
}

// 摺疊選單統一管理功能
const collapseManager = {
    // 儲存所有摺疊選單的狀態
    menuStates: {},
    
    // 初始化：從localStorage讀取已保存的狀態
    init: function() {
        const savedStates = localStorage.getItem('collapseMenuStates');
        if (savedStates) {
            try {
                this.menuStates = JSON.parse(savedStates);
            } catch (e) {
                console.error('解析摺疊選單狀態失敗:', e);
                this.menuStates = {};
            }
        }
    },
    
    // 保存狀態到localStorage
    saveStates: function() {
        localStorage.setItem('collapseMenuStates', JSON.stringify(this.menuStates));
    },
    
    // 設置選單狀態
    setMenuState: function(menuId, isOpen) {
        this.menuStates[menuId] = isOpen;
        this.saveStates();
    },
    
    // 獲取選單狀態
    getMenuState: function(menuId) {
        return this.menuStates[menuId] === true;
    },
    
    // 切換選單狀態
    toggleMenuState: function(menuId, toggleBtn, isOpen) {
        // 如果isOpen未定義，則使用toggleBtn的collapsed類來判斷
        const state = (isOpen !== undefined) ? isOpen : toggleBtn.classList.contains('collapsed');
        this.setMenuState(menuId, state);
    },
    
    // 應用選單狀態到UI (無動畫版本)
    applyMenuStateWithoutAnimation: function(menuId, collapseElement, toggleBtn) {
        const isOpen = this.getMenuState(menuId);
        console.log(123, collapseElement)
        // 先禁用動畫
        collapseElement.classList.add('no-transition');

        if (isOpen) {
            // 如果應該打開，直接添加show類
            collapseElement.classList.add('show');
            toggleBtn.classList.add('collapsed');
            toggleBtn.setAttribute('aria-expanded', 'true');
        } else {
            // 如果應該關閉，直接移除show類
            collapseElement.classList.remove('show');
            toggleBtn.classList.remove('collapsed');
            toggleBtn.setAttribute('aria-expanded', 'false');
        }
        
    },
    
    // 應用選單狀態到UI (有動畫版本)
    applyMenuState: function(menuId, collapseElement, toggleBtn) {
        const isOpen = this.getMenuState(menuId);
        
        // 如果需要打開並且當前是關閉的
        if (isOpen && !toggleBtn.classList.contains('collapsed')) {
            // 使用Bootstrap的collapse API打開選單
            new bootstrap.Collapse(collapseElement, { toggle: true });
            toggleBtn.classList.add('collapsed');
        } 
        // 如果需要關閉並且當前是打開的
        else if (!isOpen && toggleBtn.classList.contains('collapsed')) {
            // 使用Bootstrap的collapse API關閉選單
            new bootstrap.Collapse(collapseElement, { toggle: false });
            toggleBtn.classList.remove('collapsed');
        }
    },
    
    // 啟用所有折疊元素的動畫
    enableAnimations: function() {
        document.querySelectorAll('.collapse').forEach(el => {
            el.classList.remove('no-transition');
        });
    }
};

// 禁用Bootstrap折疊動畫的全局樣式
function addNoTransitionStyle() {
    const style = document.createElement('style');
    style.id = 'noTransitionStyle';
    style.innerHTML = `
        .collapse.no-transition {
            transition: none !important;
            animation: none !important;
        }

        .side-bar.no-sidebar-transition,
        .side-bar.no-sidebar-transition * {
            transition: none !important;
            animation: none !important;
        }
    `;
    document.head.appendChild(style);
}

// 初始化摺疊選單事件處理
function initializeCollapseMenu(menuId, toggleId, collapseId) {
    const toggleBtn = document.getElementById(toggleId);
    const collapseElement = document.getElementById(collapseId);
    
    if (!toggleBtn || !collapseElement) return;
    
    // 點擊切換按鈕時
    toggleBtn.addEventListener('click', function() {
        this.classList.toggle('collapsed');
        // 下一個事件循環執行，確保collapsed類已經被切換
        setTimeout(() => {
            collapseManager.toggleMenuState(menuId, this);
        }, 0);
    });
    
    // 監聽collapse事件
    collapseElement.addEventListener('hidden.bs.collapse', function() {
        toggleBtn.classList.remove('collapsed');
        collapseManager.setMenuState(menuId, false);
    });
    
    collapseElement.addEventListener('shown.bs.collapse', function() {
        toggleBtn.classList.add('collapsed');
        collapseManager.setMenuState(menuId, true);
    });
}

// 檢查側邊欄狀態 (頁面載入時)
window.addEventListener('DOMContentLoaded', function() {
    // 添加無動畫樣式
    addNoTransitionStyle();
    
    const sidebar = document.getElementById('sidebar');
    if (!sidebar) return;
    
    // 先禁用側邊欄的過渡動畫
    sidebar.classList.add('no-sidebar-transition');
    
    // 應用側邊欄折疊狀態
    applySidebarState();
    
    // 初始化摺疊選單管理器
    collapseManager.init();
    
    // 初始化每個摺疊選單
    //initializeCollapseMenu('menu1', 'sidebarCollapseToggle', 'collapseItem');
    //initializeCollapseMenu('menu2', 'sidebarCollapseToggle2', 'collapseItem2');
    //initializeCollapseMenu('menuSm', 'sidebarCollapseToggle-sm', 'collapseItem-sm');
    
    // 應用已保存的摺疊選單狀態（無動畫）
    //collapseManager.applyMenuStateWithoutAnimation('menu1', document.getElementById('collapseItem'), document.getElementById('sidebarCollapseToggle'));
    //collapseManager.applyMenuStateWithoutAnimation('menu2', document.getElementById('collapseItem2'), document.getElementById('sidebarCollapseToggle2'));
    
    //const smToggle = document.getElementById('sidebarCollapseToggle-sm');
    //const smCollapse = document.getElementById('collapseItem-sm');
    //if (smToggle && smCollapse) {
    //    collapseManager.applyMenuStateWithoutAnimation('menuSm', smCollapse, smToggle);
    //}
    
    // 頁面載入完成後延遲啟用動畫
    window.addEventListener('load', function() {
        // 延遲一點時間，確保頁面已完全渲染
        setTimeout(() => {
            collapseManager.enableAnimations();
            
            // 啟用側邊欄的過渡動畫
            sidebar.classList.remove('no-sidebar-transition');
        }, 500);
    });
    
    // 初始化主題模式
    const savedTheme = localStorage.getItem('theme');
    if (savedTheme === 'light') {
        document.body.classList.add('light-mode');
        document.getElementById('themeToggle').innerHTML = '<i class="fa-solid fa-moon"></i>';
    }
    
    // 主題切換功能
    const themeToggle = document.getElementById('themeToggle');
    themeToggle.addEventListener('click', function(e) {
        e.preventDefault();
        
        document.body.classList.toggle('light-mode');
        
        if (document.body.classList.contains('light-mode')) {
            localStorage.setItem('theme', 'light');
            this.innerHTML = '<i class="fa-solid fa-moon"></i>';
        } else {
            localStorage.setItem('theme', 'dark');
            this.innerHTML = '<i class="fa-solid fa-lightbulb"></i>';
        }
    });
    
    // 初始化元素引用
    dropdownTrigger = document.getElementById('dropdownTrigger');
    dropdownMenu = document.getElementById('dropdownMenu');
    overlay = document.getElementById('overlay');
    
    // 初始化通知下拉選單
    console.log('初始化下拉選單:', {
        dropdownTrigger: dropdownTrigger,
        dropdownMenu: dropdownMenu,
        overlay: overlay
    });
    
    // 點擊遮罩關閉下拉選單
    overlay.addEventListener('click', closeDropdown);
    
    // 點擊其他區域關閉下拉選單
    document.addEventListener('click', function(e) {
        if (dropdownTrigger && dropdownMenu && !dropdownTrigger.contains(e.target) && !dropdownMenu.contains(e.target)) {
            closeDropdown();
        }
    });
    
    // 按ESC鍵關閉下拉選單
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            closeDropdown();
        }
    });
    
    // 阻止下拉選單的滾動事件傳播到父元素
    dropdownMenu.addEventListener('wheel', function(e) {
        // 檢查是否已經滾動到頂部或底部
        const isScrolledToTop = this.scrollTop === 0;
        const isScrolledToBottom = this.scrollHeight - this.scrollTop === this.clientHeight;
        
        // 如果向上滾動且已經在頂部，或向下滾動且已經在底部，則阻止事件
        if ((e.deltaY < 0 && isScrolledToTop) || (e.deltaY > 0 && isScrolledToBottom)) {
            e.preventDefault();
        }
        
        // 停止事件冒泡，防止影響父元素
        e.stopPropagation();
    }, { passive: false });
    
    // 菜單項點擊事件
    const menuItems = document.querySelectorAll('.side-bar-menu-item');
    menuItems.forEach(item => {
        item.addEventListener('click', function() {
            menuItems.forEach(i => i.classList.remove('active'));
            this.classList.add('active');
        });
    });
});

// 全螢幕切換功能
const fullscreenToggle = document.getElementById('fullscreenToggle');
fullscreenToggle.addEventListener('click', function(e) {
    e.preventDefault();
    
    toggleFullScreen();
});

// 處理各種瀏覽器的全螢幕模式
function toggleFullScreen() {
    const doc = window.document;
    const docEl = doc.documentElement;
    
    const requestFullScreen = docEl.requestFullscreen || docEl.mozRequestFullScreen || docEl.webkitRequestFullScreen || docEl.msRequestFullscreen;
    const cancelFullScreen = doc.exitFullscreen || doc.mozCancelFullScreen || doc.webkitExitFullscreen || doc.msExitFullscreen;
    
    if (!doc.fullscreenElement && !doc.mozFullScreenElement && !doc.webkitFullscreenElement && !doc.msFullscreenElement) {
        // 進入全螢幕
        if (requestFullScreen) {
            requestFullScreen.call(docEl);
        }
    } else {
        // 退出全螢幕
        if (cancelFullScreen) {
            cancelFullScreen.call(doc);
        }
    }
}

// 監聽全螢幕狀態變化
function updateFullscreenButton() {
    const isFullScreen = document.fullscreenElement || document.mozFullScreenElement || document.webkitFullscreenElement || document.msFullscreenElement;
    
    if (!isFullScreen) {
        fullscreenToggle.innerHTML = '<i class="fa-solid fa-expand"></i>';
    } else {
        fullscreenToggle.innerHTML = '<i class="fa-solid fa-compress"></i>';
    }
}

document.addEventListener('fullscreenchange', updateFullscreenButton);
document.addEventListener('webkitfullscreenchange', updateFullscreenButton);
document.addEventListener('mozfullscreenchange', updateFullscreenButton);
document.addEventListener('MSFullscreenChange', updateFullscreenButton);

// 下拉選單功能
function toggleDropdown(event) {
    if (event) {
        event.preventDefault();
        event.stopPropagation();
    }
    
    dropdownMenu = document.getElementById('dropdownMenu');
    overlay = document.getElementById('overlay');
    
    const isActive = dropdownMenu.classList.contains('active');
    
    console.log('切換下拉選單:', isActive ? '關閉' : '打開');
    
    if (!isActive) {
        // 顯示下拉選單
        dropdownMenu.style.display = 'block';
        dropdownMenu.classList.add('active');
        overlay.classList.add('active');
        
        // 調整位置到標題下方
        const contentHeader = document.querySelector('.content-header');
        if (contentHeader) {
            const headerRect = contentHeader.getBoundingClientRect();
            dropdownMenu.style.top = 30 + 'px';
            dropdownMenu.style.left = headerRect.left + 'px';
        }
    } else {
        // 隱藏下拉選單
        closeDropdown();
    }
}

function closeDropdown() {
    console.log('關閉下拉選單');
    dropdownMenu.classList.remove('active');
    overlay.classList.remove('active');
    
    // 設置延遲後隱藏選單
    setTimeout(() => {
        if (!dropdownMenu.classList.contains('active')) {
            dropdownMenu.style.display = 'none';
        }
    }, 300);
}

// 對外公開
window.toggleSidebar = toggleSidebar;
window.toggleDropdown = toggleDropdown;
window.closeDropdown = closeDropdown;
window.applySidebarState = applySidebarState;

// 監聽Blazor路由變化事件，在頁面導航時保持側邊欄狀態
document.addEventListener('DOMContentLoaded', function() {
    // MutationObserver用於監聽DOM變化，以檢測Blazor頁面導航
    const bodyObserver = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
            if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                // 頁面已經導航，應用保存的側邊欄狀態
                applySidebarState();
            }
        });
    });
    
    // 在body上設置觀察器，監聽子節點變化
    bodyObserver.observe(document.body, { childList: true, subtree: true });
});