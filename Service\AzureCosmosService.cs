using Microsoft.Azure.Cosmos;
using Microsoft.Extensions.Configuration; // 用於讀取組態
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Dashboard_Yuntech.Models.AzureCosmos;
using Microsoft.Extensions.Caching.Memory;
using System.Collections;

public class AzureCosmosService
{
    private readonly CosmosClient _cosmosClient;
    private readonly string _databaseId;
    private readonly Dictionary<string, Container> _containers;
    private readonly IMemoryCache _cache;

    public AzureCosmosService(IConfiguration configuration, IMemoryCache cache)
    {
        var endpointUri = configuration["CosmosDb:EndpointUri"];
        var primaryKey = configuration["CosmosDb:PrimaryKey"];
        _databaseId = configuration["CosmosDb:DatabaseId"];
        _containers = new Dictionary<string, Container>();
        _cache = cache;

        _cosmosClient = new CosmosClient(endpointUri, primaryKey);
    }

    private async Task<Container> GetContainerAsync(string containerId)
    {
        if (!_containers.ContainsKey(containerId))
        {
            Database database = await _cosmosClient.CreateDatabaseIfNotExistsAsync(_databaseId);
            _containers[containerId] = await database.CreateContainerIfNotExistsAsync(containerId, "/年度");
        }
        return _containers[containerId];
    }

    // 可以讀取任何容器的資料 (帶快取功能)
    public async Task<IEnumerable<T>> GetItemsAsync<T>(ContainerType containerType, string query = "SELECT * FROM c") where T : class
    {
        // 建立快取鍵值
        var cacheKey = $"cosmos_{containerType.GetContainerName()}_{typeof(T).Name}_{query.GetHashCode()}";

        // 嘗試從快取中取得資料
        if (_cache.TryGetValue(cacheKey, out IEnumerable<T> cachedData))
        {
            return cachedData;
        }

        var containerId = containerType.GetContainerName();
        var container = await GetContainerAsync(containerId);

        var queryDefinition = new QueryDefinition(query);
        // 使用泛型 T 來指定反序列化的目標型別
        var queryResultSetIterator = container.GetItemQueryIterator<T>(queryDefinition);

        var results = new List<T>();
        while (queryResultSetIterator.HasMoreResults)
        {
            // FeedResponse 也會是泛型
            FeedResponse<T> currentResultSet = await queryResultSetIterator.ReadNextAsync();
            foreach (T item in currentResultSet)
            {
                results.Add(item);
            }
        }

        // 將結果存入快取，設定5分鐘過期時間
        var cacheOptions = new MemoryCacheEntryOptions
        {
            AbsoluteExpirationRelativeToNow = TimeSpan.FromMinutes(5),
            SlidingExpiration = TimeSpan.FromMinutes(2),
            Priority = CacheItemPriority.Normal
        };

        _cache.Set(cacheKey, results, cacheOptions);

        return results;
    }

    // 清除特定容器的快取
    // 注意：由於 IMemoryCache 沒有提供清除特定前綴快取的方法，
    // 這裡提供一個簡化的介面，實際上需要記住快取鍵來手動清除
    public void ClearCache(ContainerType containerType)
    {
        // 這是一個簡化的實作，實際使用時可能需要維護快取鍵的清單
        // 或使用第三方快取解決方案如 Redis
        var containerName = containerType.GetContainerName();

        // 由於無法直接枚舉 IMemoryCache 的鍵，這裡只提供介面
        // 實際清除需要在應用程式中維護快取鍵的清單
        // 或者等待快取自然過期
    }

    // 清除所有快取
    // 注意：這是一個簡化的實作
    public void ClearAllCache()
    {
        // 由於 IMemoryCache 沒有提供清除所有項目的方法，
        // 這裡只提供介面，實際清除需要重新建立 MemoryCache 實例
        // 或者等待快取自然過期
    }
}