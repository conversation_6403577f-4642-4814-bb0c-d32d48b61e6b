using System.Text;
using HtmlAgilityPack;

namespace Dashboard_Yuntech.Service
{
    public class WebScrapingService
    {
        private readonly IHttpClientFactory _httpClientFactory;

        public WebScrapingService(IHttpClientFactory httpClientFactory)
        {
            _httpClientFactory = httpClientFactory;
        }

        /// <summary>
        /// 爬取 Cacti 網頁並獲取 CSV 下載連結
        /// </summary>
        /// <param name="url">Cacti 圖表頁面 URL</param>
        /// <param name="timeRange">時間範圍 (Hourly, Daily, Weekly, Monthly, Yearly)</param>
        /// <returns>CSV 下載 URL</returns>
        public async Task<string> GetCsvDownloadUrlAsync(string url, string timeRange = "Hourly")
        {
            try
            {
                var client = _httpClientFactory.CreateClient();

                // 設置 User-Agent 避免被阻擋
                client.DefaultRequestHeaders.Add("User-Agent",
                    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36");

                var response = await client.GetAsync(url);

                if (!response.IsSuccessStatusCode)
                {
                    throw new Exception($"無法訪問網頁: {response.StatusCode}");
                }

                var html = await response.Content.ReadAsStringAsync();

                // 使用 HtmlAgilityPack 解析 HTML
                var doc = new HtmlDocument();
                doc.LoadHtml(html);

                // 找到所有包含 graph_xport.php 的連結
                var csvLinks = doc.DocumentNode
                    .SelectNodes("//a[@href and contains(@href, 'graph_xport.php')]");

                if (csvLinks == null || csvLinks.Count == 0)
                {
                    throw new Exception("找不到 CSV 下載連結");
                }

                // 根據時間範圍選擇對應的連結
                HtmlNode? targetLink = null;

                foreach (var link in csvLinks)
                {
                    // 找到包含此連結的表格
                    var parentTable = link.Ancestors("table").FirstOrDefault();
                    if (parentTable != null)
                    {
                        var tableText = parentTable.InnerText;

                        // 根據時間範圍匹配
                        if (timeRange == "Hourly" && tableText.Contains("Hourly (1 Minute Average)"))
                        {
                            targetLink = link;
                            break;
                        }
                        else if (timeRange == "Daily" && tableText.Contains("Daily (5 Minute Average)"))
                        {
                            targetLink = link;
                            break;
                        }
                        else if (timeRange == "Weekly" && tableText.Contains("Weekly (30 Minute Average)"))
                        {
                            targetLink = link;
                            break;
                        }
                        else if (timeRange == "Monthly" && tableText.Contains("Monthly (2 Hour Average)"))
                        {
                            targetLink = link;
                            break;
                        }
                        else if (timeRange == "Yearly" && tableText.Contains("Yearly (1 Day Average)"))
                        {
                            targetLink = link;
                            break;
                        }
                    }
                }

                // 如果找不到指定時間範圍的連結，使用第一個連結
                if (targetLink == null)
                {
                    targetLink = csvLinks.First();
                }

                var selectedCsvUrl = targetLink.GetAttributeValue("href", "");

                // 如果是相對路徑，轉換為絕對路徑
                if (selectedCsvUrl.StartsWith("/") || !selectedCsvUrl.StartsWith("http"))
                {
                    var baseUri = new Uri(url);
                    var fullUrl = new Uri(baseUri, selectedCsvUrl).ToString();
                    return fullUrl;
                }

                return selectedCsvUrl;
            }
            catch (Exception ex)
            {
                throw new Exception($"爬取網頁時發生錯誤: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 下載 CSV 資料
        /// </summary>
        /// <param name="csvUrl">CSV 下載 URL</param>
        /// <returns>CSV 內容</returns>
        public async Task<string> DownloadCsvAsync(string csvUrl)
        {
            try
            {
                var client = _httpClientFactory.CreateClient();
                
                // 設置 User-Agent
                client.DefaultRequestHeaders.Add("User-Agent", 
                    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36");

                var response = await client.GetAsync(csvUrl);
                
                if (!response.IsSuccessStatusCode)
                {
                    throw new Exception($"下載 CSV 失敗: {response.StatusCode}");
                }

                var csvContent = await response.Content.ReadAsStringAsync();
                return csvContent;
            }
            catch (Exception ex)
            {
                throw new Exception($"下載 CSV 時發生錯誤: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 爬取網頁並直接下載 CSV 資料
        /// </summary>
        /// <param name="url">Cacti 圖表頁面 URL</param>
        /// <param name="timeRange">時間範圍</param>
        /// <returns>CSV 內容</returns>
        public async Task<string> ScrapeAndDownloadCsvAsync(string url, string timeRange = "Hourly")
        {
            var csvUrl = await GetCsvDownloadUrlAsync(url, timeRange);
            return await DownloadCsvAsync(csvUrl);
        }

        /// <summary>
        /// 解析 CSV 內容為資料表格式
        /// </summary>
        /// <param name="csvContent">CSV 內容</param>
        /// <returns>解析後的資料</returns>
        public List<Dictionary<string, object>> ParseCsvContent(string csvContent)
        {
            var result = new List<Dictionary<string, object>>();
            
            if (string.IsNullOrWhiteSpace(csvContent))
                return result;

            var lines = csvContent.Split('\n', StringSplitOptions.RemoveEmptyEntries);
            
            if (lines.Length < 2)
                return result;

            // 第一行是標題
            var headers = lines[0].Split(',').Select(h => h.Trim('"')).ToArray();
            
            // 處理資料行
            for (int i = 1; i < lines.Length; i++)
            {
                var values = lines[i].Split(',');
                var row = new Dictionary<string, object>();
                
                for (int j = 0; j < Math.Min(headers.Length, values.Length); j++)
                {
                    var value = values[j].Trim('"');
                    
                    // 嘗試轉換為數字
                    if (double.TryParse(value, out double numValue))
                    {
                        row[headers[j]] = numValue;
                    }
                    else if (DateTime.TryParse(value, out DateTime dateValue))
                    {
                        row[headers[j]] = dateValue;
                    }
                    else
                    {
                        row[headers[j]] = value;
                    }
                }
                
                result.Add(row);
            }
            
            return result;
        }
    }
}
