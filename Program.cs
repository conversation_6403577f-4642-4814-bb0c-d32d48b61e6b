using Dashboard_Yuntech.Components;
using Microsoft.AspNetCore.Authentication.Cookies;
using Dashboard_Yuntech.DbCon;
using Microsoft.EntityFrameworkCore;
using System.Security.Cryptography;
using Dashboard_Yuntech.Service;
using Blazorise;
using Blazorise.Bootstrap5;
using Blazorise.Icons.FontAwesome;
using ApexCharts;
using OfficeOpenXml;
using Dashboard_Yuntech.Controllers;

var builder = WebApplication.CreateBuilder(args);

// UService
builder.Services.AddScoped<DataService>();
builder.Services.AddScoped<DbContextHelper>();
builder.Services.AddScoped<BaseService>();
builder.Services.AddScoped<ExcelService>();
builder.Services.AddScoped<ChartDataService>();
builder.Services.AddScoped<ChartConfigurationService>();
builder.Services.AddScoped<AzureCosmosService>();
builder.Services.AddScoped<WebScrapingService>();
builder.Services.AddScoped(sp => new HttpClient());
ExcelPackage.LicenseContext = LicenseContext.NonCommercial;

// Add services to the container.
builder.Services.AddRazorComponents()
    .AddInteractiveServerComponents();

// 添加 API 控制器支持
builder.Services.AddControllers();

// UI
builder.Services
    .AddBlazorise(options =>
    {
        options.Immediate = true;
    })
    .AddBootstrap5Providers()
    .AddFontAwesomeIcons();

// ApexCharts
builder.Services.AddApexCharts(options =>
{
    options.GlobalOptions = new ApexChartBaseOptions
    {
        Debug = true,
        Theme = new ApexCharts.Theme
        {
            Mode = Mode.Dark,
            Palette = PaletteType.Palette1
        }
    };
});


// ���U QuickGrid �A��
builder.Services.AddQuickGridEntityFrameworkAdapter();

// �]�w cookie
builder.Services.AddAuthentication(CookieAuthenticationDefaults.AuthenticationScheme)
    .AddCookie(x =>
    {
        x.LoginPath = "/login";
    });
builder.Services.AddAuthorization();
builder.Services.AddCascadingAuthenticationState();

builder.Services.AddHttpContextAccessor(); // �T�O���U IHttpContextAccessor

var config = builder.Configuration;
var services = builder.Services;

services.AddRazorComponents();


services.AddDistributedMemoryCache(); // Use a memory cache to store session
services.AddMemoryCache(); // Add IMemoryCache for Azure Cosmos DB caching

// Add IHttpClientFactory
services.AddHttpClient();

// �]�w Session
services.AddSession(options =>
{
    options.IdleTimeout = TimeSpan.FromMinutes(30); // Set session timeout
    options.Cookie.HttpOnly = true;
    options.Cookie.IsEssential = true;
});

services.AddHttpContextAccessor();


// �]�w��Ʈw
services.AddDbContext<OracleDbContext>(options =>
    options.UseOracle(config.GetConnectionString("Oracle"))
);
services.AddDbContext<ASH_DbContext>(
    x => x.UseSqlServer(config.GetConnectionString("dbASH"))
);

var app = builder.Build();

// Configure the HTTP request pipeline.
if (!app.Environment.IsDevelopment())
{
    app.UseExceptionHandler("/Error", createScopeForErrors: true);
    // The default HSTS value is 30 days. You may want to change this for production scenarios, see https://aka.ms/aspnetcore-hsts.
    app.UseHsts();
}

app.UseHttpsRedirection();
app.UseSession(); // �K�[�o�@��
app.UseAuthentication();
app.UseAuthorization();
app.UseAntiforgery();// Add the middleware to set security headers and CSP
// Add the middleware to set security headers and CSP
//app.Use(async (context, next) =>
//{
//    var nonce = Convert.ToBase64String(Guid.NewGuid().ToByteArray());
//    context.Response.Headers.Append("X-Frame-Options", "SAMEORIGIN");
//    context.Response.Headers["X-XSS-Protection"] = "0";
//    context.Response.Headers["X-Content-Type-Options"] = "nosniff";
//    context.Response.Headers["Content-Security-Policy"] = "base-uri 'self'; " +
//        "block-all-mixed-content; " +
//        "default-src 'self'; " +
//        "connect-src 'self' wss://localhost:* http://localhost:*/ ws://localhost:*/; " +
//        "style-src 'self' https://cdn.jsdelivr.net https://use.fontawesome.com 'nonce-" + nonce + "'; " +
//        "font-src 'self' data: https://use.fontawesome.com; " +
//        "form-action 'self'; " +
//        "frame-ancestors 'none'; " +
//        "frame-src 'self' blob:; " +
//        "img-src 'self' data: https://webapp.yuntech.edu.tw https://test-web1.yuntech.edu.tw; " +
//        "object-src 'none'; " +
//        "script-src 'self' https://cdn.jsdelivr.net 'nonce-" + nonce + "'; " +
//        "upgrade-insecure-requests; " +
//        "worker-src 'self' blob:;"
//;

//    // Store nonce in the context for later use
//    context.Items["cspNonce"] = nonce;

//    await next();
//});
app.UseStaticFiles();

app.MapRazorComponents<App>()
    .AddInteractiveServerRenderMode();

// 添加 API 路由支持
app.MapControllers();

app.Run();
