﻿// 日期選擇器插件
// type 為text，class有 datePicker => 觸發套件
window.DatePicker = () =>
{
    console.log('DatePicker');
    let todayBtn = {
        content: '今天',
        className: 'custom-button-today',
        onClick: (dp) =>
        {
            let date = new Date();
            dp.selectDate(date);
            dp.setViewDate(date);
        }
    }

    // 設定在當年度
    const currentYear = new Date().getFullYear();

    document.querySelectorAll('input[type="text"].date, input[type="date"].date').forEach(element =>
    {
        new AirDatepicker(element, {
            locale: zh,
            autoClose: true,
            dateFormat: 'yyyy/MM/dd',
            buttons: ['clear', todayBtn],
            onSelect: ({ date, formattedDate }) =>
            {
                element.dispatchEvent(new Event('change'));
            }
        });
    });
};


// Sweetalert2 toast
window.showColoredToast = (type, message) =>
{
    Swal.mixin({
        toast: true,
        position: 'top-end',
        icon: type, // success
        title: message, // 成功
        iconColor: 'white',
        showConfirmButton: false,
        showCloseButton: true,
        timer: 3000,
        timerProgressBar: true,
        customClass: {
            popup: 'colored-toast',
            title: 'colored-toast-title',
            closeButton: 'colored-toast-close-button'
        },
        didOpen: (toast) =>
        {
            toast.addEventListener('mouseenter', Swal.stopTimer)
            toast.addEventListener('mouseleave', Swal.resumeTimer)
        }
    }).fire();
};


window.Modalshow = (modalId) =>
{
    const modalElement = document.querySelector(modalId);
    if (modalElement)
    {
        const modal = new bootstrap.Modal(modalElement);
        modal.show();
    } 
}

window.ModalHide = (modalId) =>
{
    const modalElement = document.querySelector(modalId);
    if (modalElement)
    {
        const modal = new bootstrap.Modal(modalElement);
        modal.hide();
    }
}

// 下載檔案函數
window.saveAsFile = function (filename, bytesBase64)
{
    console.log(321)
    var link = document.createElement('a');
    link.download = filename;
    link.href = "data:application/octet-stream;base64," + bytesBase64;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}